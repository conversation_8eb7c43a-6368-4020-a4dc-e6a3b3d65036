import React, { useContext, useState, useEffect, useRef, useMemo, memo } from 'react';
import {
    StyleSheet,
    ActivityIndicator,
    View,
    PermissionsAndroid,
    Platform,
    Text,
    Linking,
    Button,
    NativeEventEmitter,
} from 'react-native';
import MapboxGL from '@rnmapbox/maps';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { OnPressEvent } from '@rnmapbox/maps/lib/typescript/src/types/OnPressEvent';

//Components, contexts imports
import { AuthContext } from '../../context/AuthContext';
//@ts-ignore
import { LocationContext } from '../../context/LocationContext';
import ListView from './ListView';
import MapSearchResults from '../search/MaPSearchResults';
import UserLocationButton from '../buttons/UserLocationButton';
import ClubDetailsPopup from './ClubDetailsPopup';
import { GlobalContext } from '../../context/contextApi';
import MapCluster from './MapCluster';
import ScreenResizeButton from '../buttons/ScreenResizeButton';

//Services, utils, interfaces, constants, types and assets imports
import { CLUB_DETAILS_MAP } from '../../service/EndPoint';
import { fetcher } from '../../service/fetcher';
import { getRecommendedClubs } from '../../screens/requests/Utils';
import constants, { tiers } from '../../utils/constants/constants';
import { MapClub, MapClubDetail, MapRegion, UserClub } from '../../interface';
import { RootStackParamList } from '../../interface/type';
import { HIGHLY_REQUESTED, UNEXPLORED } from '../../utils/constants/strings';

//CleverTap import
const CleverTap = require('clevertap-react-native');

interface ClubMapProps {
    clubs: MapClub[];
    setMapClubs: (clubs: MapClub[]) => void;
    activeView: string;
    setActiveView: (activeView: string) => void;
    locations: any[];
    searchingState: [boolean, (searching: boolean) => void];
    searchInputState: [string, (searchInput: string) => void];
    selectedClub: MapClubDetail | null;
    setSelectedClub: (selectedClub: MapClubDetail | null) => void;
    searchedClubList: MapClub[];
    moreDetailListView: boolean;
    setMoreDetailListView: (moreDetailListView: boolean) => void;
    isComeFromMap: boolean;
    setIsComeFromMap: (isComeFromMap: boolean) => void;
    zoomLevel: number;
    setZoomLevel: (zoomLevel: number) => void;
    clubFromRoute: number | null | undefined;
}

function ClubMap({
    clubs,
    setMapClubs,
    activeView,
    setActiveView,
    locations,
    searchingState,
    searchInputState,
    selectedClub,
    setSelectedClub,
    searchedClubList,
    moreDetailListView,
    setMoreDetailListView,
    isComeFromMap,
    setIsComeFromMap,
    zoomLevel,
    setZoomLevel,
    clubFromRoute,
}: ClubMapProps) {
    console.log('clubFromRoute', clubFromRoute);
    const { user } = useContext(AuthContext);
    const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
    const { state, actions } = useContext(GlobalContext);
    //@ts-ignore
    const { userLocation, getLocation } = useContext(LocationContext);
    const [locationPermissionGranted, setLocationPermissionGranted] = useState(false);
    const [searching, setSearching] = searchingState;
    const [centerCoordinates, setCenterCoordinates] = useState([
        state?.mapCurrentState?.lng || 37.0902,
        state?.mapCurrentState?.lat || 95.7129,
    ]);
    const camera = useRef(null);
    const map = useRef(null);
    const [searchType, setSearchType] = useState('');
    const [ownClub, setOwnClub] = useState<number[]>([]);
    const [requestedClub, setRequestedClub] = useState<MapClub | null>(null);
    const [recommendedClubs, setRecommendedClubs] = useState<MapClub[]>([]);

    let appStateEmitter: NativeEventEmitter;
    useEffect(() => {
        actions.setAppLoader(true);
        if (Platform.OS === 'android') appStateEmitter = new NativeEventEmitter();
    }, []);

    useEffect(() => {
        if (clubFromRoute && clubs?.length) {
            const club = clubs.find((club: MapClub) => club?.id === clubFromRoute);
            actions.setMapCurrentClub(club as unknown as MapClub);
            setTimeout(() => {
                navigation.navigate('MapDetails');
            }, 1000);
        }
    }, [clubFromRoute, clubs]);

    const newClub: MapClub[] = useMemo(() => {
        return clubs?.map((item: MapClub) => {
            if (item.geometry.coordinates[0] === null && item.geometry.coordinates[1] === null) {
                return {
                    ...item,
                    geometry: {
                        type: 'Point',
                        coordinates: [0, 0],
                    },
                };
            } else {
                return item;
            }
        });
    }, [clubs]);

    // This function is used to change the region nad location on tap on any searched location
    async function animateMapToBoundaries(params: { location: { lat: number; lng: number } }) {
        setActiveView('MAP');
        setSearching(false);
        const { location } = params;
        changeRegion(location?.lat, location?.lng, false);
    }

    const checkAndroidPerms = () => {
        PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION)
            .then((permission: any) => {
                if (!permission) {
                    getAndroidPermission();
                } else {
                    setLocationPermissionGranted(permission === 'denied' ? false : permission);
                }
            })
            .catch((err) => {});
    };
    const getAndroidPermission = () => {
        PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION)
            .then((permission: any) => {
                if (locationPermissionGranted !== permission) {
                    setLocationPermissionGranted(permission === 'denied' ? false : permission);
                }
            })
            .catch((err) => {});
    };

    const handleClubPress = ({ id, properties: { color } }: { id: number; properties: { color: string } }) => {
        actions.setAppLoader(true);
        CleverTap.recordEvent(constants.CLEVERTAP.EVENTS.GOLF_CLUBS, { clubId: id });
        const body = {
            userId: user?.id,
            clubId: Number(id),
            clubColor: color,
        };
        fetcher({
            endpoint: CLUB_DETAILS_MAP,
            method: 'POST',
            body,
        })
            .then((data) => {
                const { lat, lng } = data?.clubs;
                if (lat && lng) {
                    setActiveView('MAP');
                    setSelectedClub(data);
                    actions.setAppLoader(false);
                    // actions?.setZoomLevelState(data?.properties?.zoomLevel);
                    // actions.setMapCurrentClub(data); // Set current club on which user clicked and it is used to open club details when user come from another tab
                }
            })
            .catch((err) => {});
    };

    // This function called when we change the region on map
    const handleRegionChange = async (data: MapRegion) => {
        try {
            //If the coordinates are same then do not change the map state
            if (
                state?.mapCurrentState?.lat === data?.geometry?.coordinates[1] &&
                state?.mapCurrentState?.lng === data?.geometry?.coordinates[0]
            ) {
                return;
            }
            actions?.setMapCurrentState({
                lat: data?.geometry?.coordinates[1],
                lng: data?.geometry?.coordinates[0],
            });

            // actions?.setZoomLevelState(data?.properties?.zoomLevel);
            // actions.setMapCurrentClub(); // Set current club on which user clicked and it is used to open club details when user come from another tab
            actions.setMapBoundaries(data?.properties?.visibleBounds);
        } catch (error) {
            console.log('handleRegionChange --->', error);
        }
    };

    // This function is used for move the map to the searched or current location
    const changeRegion = async (latitude: number, longitude: number, isZoomNeeded: boolean) => {

        // Validate coordinates
        if (!latitude || !longitude || isNaN(latitude) || isNaN(longitude)) {
            return;
        }

        if (Platform.OS === 'ios') {
            // For iOS, use direct camera control
            setCenterCoordinates([longitude, latitude]);

            setTimeout(() => {
                if (camera.current) {
                    //@ts-ignore
                    camera.current.setCamera({
                        centerCoordinate: [longitude, latitude],
                        zoomLevel: isZoomNeeded ? 12 : zoomLevel,
                        animationDuration: 1000,
                    });
                }
            }, 300);
        } else {
            // Android implementation
            //@ts-ignore
            camera.current?.moveTo([longitude, latitude]);
            setCenterCoordinates([Number(longitude), Number(latitude)]);
            if (isZoomNeeded) {
                setTimeout(() => {
                    //@ts-ignore
                    camera.current?.zoomTo(10, 100);
                }, 300);
            }
        }
    };

    //this function is call when we click on any club marker
    const onPressClubMarker = (club: MapClub) => {
        setIsComeFromMap(true);
        actions?.setClubDetails(club);
        handleClubPress(club);
    };

    //this function is call when we click on any club in list view
    const onPressClub = (club: MapClub) => {
        CleverTap.recordEvent(constants.CLEVERTAP.CLICK_CLUB_CARD, {
            'Club name': club.properties.name,
            'Club Address': club.properties.addr,
            'User email': user.email,
            'User tier': user.tier, // e.g., Gold / Silver / Bronze / Platinum
            'User Membership': user.membership_plan?.name, // Active / Inactive
            'Locator colour': club.properties.color, // Blue / Green / Grey
        });
        setIsComeFromMap(false);
        setMoreDetailListView(true);
        actions?.setClubDetails(club);
        handleClubPress(club);
        actions.setMapCurrentClub(club);
        actions.setShouldDetailBottomSheetOpen(true);
    };

    const onMarkerPressed = async (event: OnPressEvent) => {
        const feature = event.features[0];
        if (feature.properties?.cluster) {
            const zoom =
                //@ts-ignore
                Math.round(map.current?.state?.region?.properties?.zoomLevel) > 6
                    ? 6
                    : //@ts-ignore
                      Math.round(map.current?.state?.region?.properties?.zoomLevel);
            const geometry = feature.geometry as unknown as { coordinates: [number, number] };
            const coordinates = geometry.coordinates;
            //@ts-ignore
            camera.current?.moveTo(coordinates);
            setCenterCoordinates(coordinates);
            actions?.setMapCurrentState({ type: 'Point', coordinates });
            //@ts-ignore
            setZoomLevel(zoom + 2, 0);
            //@ts-ignore
            camera.current?.zoomTo(zoom + 2, 0);
        } else {
            CleverTap.recordEvent(constants.CLEVERTAP.CLICK_MAP_PIN, {
                'Locator colour': feature.properties?.color, // or "Green" / "Grey"
                'Club name': feature.properties?.name,
                'Club Tier': tiers[feature.properties?.tier], // e.g., Gold / Silver / Bronze
                'Club Type':
                    feature.properties?.clubDemandType === 1
                        ? HIGHLY_REQUESTED
                        : feature.properties?.clubDemandType === 2
                        ? UNEXPLORED
                        : null, // Neutral / Highly Requested / Unexplored
                'Club Address': feature.properties?.addr,
                'User email': user.email,
                'User tier': user.tier, // e.g., Platinum / Gold / Silver
                'User Membership': user.membership_plan?.name, // or Inactive
                'User City': user.stripe_customer_info.address.city,
                'User State': user.stripe_customer_info.address.state,
                'User Country:': user.stripe_customer_info.address.country,
                'Club in Current Location or not': 'No', // Yes / No
            });
            actions.setShouldDetailBottomSheetOpen(true);
            onPressClubMarker(feature as unknown as MapClub);
            actions.setMapCurrentClub(feature as unknown as MapClub);
        }
    };

    useEffect(() => {
        let temporaryClub: number[] = [];
        user?.clubs?.map((club: UserClub) => {
            temporaryClub.push(club?.club_id);
        });
        setOwnClub(temporaryClub);
    }, [user]);

    // this useEffect is used to set current user location
    useEffect(() => {
        if (userLocation != null) {

            // Validate coordinates first
            if (
                !userLocation.latitude ||
                !userLocation.longitude ||
                isNaN(userLocation.latitude) ||
                isNaN(userLocation.longitude)
            ) {
                // Use default US coordinates
                const defaultLat = 37.0902;
                const defaultLng = -95.7129;

                if (Platform.OS === 'ios') {
                    setCenterCoordinates([defaultLng, defaultLat]);
                    setTimeout(() => {
                        if (camera.current) {
                            //@ts-ignore
                            camera.current.setCamera({
                                centerCoordinate: [defaultLng, defaultLat],
                                zoomLevel: 4,
                                animationDuration: 1000,
                            });
                        }
                    }, 500);
                } else {
                    changeRegion(defaultLat, defaultLng, true);
                }
                return;
            }

            // Platform-specific handling
            if (Platform.OS === 'ios') {
                // For iOS, use direct camera control
                setCenterCoordinates([userLocation.longitude, userLocation.latitude]);
                // Use a timeout to ensure the map is ready
                setTimeout(() => {
                    if (camera.current) {
                        //@ts-ignore
                        camera.current.setCamera({
                            centerCoordinate: [userLocation.longitude, userLocation.latitude],
                            zoomLevel: 12,
                            animationDuration: 1000,
                        });
                    }
                }, 500);
            } else {
                // Keep Android implementation
                changeRegion(
                    state?.mapCurrentState?.lat || userLocation?.latitude,
                    state?.mapCurrentState?.lng || userLocation?.longitude,
                    true,
                );
            }
        }
    }, [userLocation]);

    useEffect(() => {
        if (Platform.OS === 'ios') {
            setLocationPermissionGranted(true);
        } else {
            checkAndroidPerms();
            const subscription = appStateEmitter.addListener('ActivityStateChange', (e) => {
                if (e.event === 'active') {
                    checkAndroidPerms();
                }
            });

            return () => {
                subscription.remove();
            };
        }
    }, []);

    useEffect(() => {
        if (locationPermissionGranted && Platform.OS === 'android') {
            getLocation();
        }
    }, [locationPermissionGranted]);

    useEffect(() => {
        if (locationPermissionGranted && !userLocation && Platform.OS === 'ios') {
            getLocation();

            // Add a fallback timer to use default location if userLocation isn't set
            const fallbackTimer = setTimeout(() => {
                if (!userLocation) {
                    const defaultLat = 37.0902;
                    const defaultLng = -95.7129;

                    setCenterCoordinates([defaultLng, defaultLat]);
                    if (camera.current) {
                        //@ts-ignore
                        camera.current.setCamera({
                            centerCoordinate: [defaultLng, defaultLat],
                            zoomLevel: 4,
                            animationDuration: 1000,
                        });
                    }
                }
            }, 3000);

            return () => clearTimeout(fallbackTimer);
        }
    }, [locationPermissionGranted, userLocation]);
    const handleCurrentLocation = () => {

        if (!userLocation || !userLocation.latitude || !userLocation.longitude) {
            // Use default US coordinates
            const defaultLat = 37.0902;
            const defaultLng = -95.7129;

            actions?.setMapCurrentState({
                lat: defaultLat,
                lng: defaultLng,
            });

            if (Platform.OS === 'ios') {
                setCenterCoordinates([defaultLng, defaultLat]);
                setTimeout(() => {
                    if (camera.current) {
                        //@ts-ignore
                        camera.current.setCamera({
                            centerCoordinate: [defaultLng, defaultLat],
                            zoomLevel: 4,
                            animationDuration: 1000,
                        });
                    }
                }, 300);
            } else {
                changeRegion(defaultLat, defaultLng, true);
            }
            return;
        }

        actions?.setMapCurrentState({
            lat: userLocation.latitude,
            lng: userLocation.longitude,
        });

        if (Platform.OS === 'ios') {
            console.log('ClubMap iOS: Moving to user location in handleCurrentLocation');
            setCenterCoordinates([userLocation.longitude, userLocation.latitude]);

            setTimeout(() => {
                if (camera.current) {
                    //@ts-ignore
                    camera.current.setCamera({
                        centerCoordinate: [userLocation.longitude, userLocation.latitude],
                        zoomLevel: 12,
                        animationDuration: 1000,
                    });
                }
            }, 300);
        } else {
            changeRegion(userLocation.latitude, userLocation.longitude, true);
        }
    };

    useEffect(() => {
        if (requestedClub?.id) {
            const getClub = async () => {
                const res = await getRecommendedClubs({ userId: user?.id, clubId: requestedClub?.id });
                if (res?.status === 1) {
                    if (res?.data?.length) {
                        setRecommendedClubs(res?.data);
                        navigation.navigate('Recommended Club', {
                            recommendedClubs: res?.data,
                            requestedClub,
                        });
                    }
                }
            };

            getClub();
        }
    }, [requestedClub]);

    if (
        userLocation &&
        newClub &&
        locationPermissionGranted &&
        //@ts-ignore
        locationPermissionGranted !== 'never_ask_again'
    ) {
        return (
            <View style={{ flex: 1 }}>
                <>
                    <MapboxGL.MapView
                        style={{ flex: 1 }}
                        ref={map}
                        //@ts-ignore
                        onRegionDidChange={(e: MapRegion) => {
                            handleRegionChange(e);
                        }}
                        attributionEnabled={false}
                        compassEnabled={false}
                        compassFadeWhenNorth={false}
                        scaleBarEnabled={false}
                        rotateEnabled={false}
                        onDidFinishRenderingMapFully={() => {
                            actions.setAppLoader(false);
                        }}
                        logoEnabled={false}
                        pitchEnabled={false}>
                        <MapboxGL.UserLocation visible={true} />
                        <MapboxGL.Camera
                            ref={camera}
                            minZoomLevel={4}
                            maxZoomLevel={13}
                            animationDuration={Platform.OS === 'ios' ? 1000 : 2000}
                            animationMode={Platform.OS === 'ios' ? 'linearTo' : 'moveTo'}
                            zoomLevel={
                                zoomLevel
                                    ? zoomLevel
                                    : searchType === 'club'
                                    ? 12
                                    : searchType === 'location'
                                    ? 6
                                    : state?.firstTimeInMap
                                    ? 12
                                    : 12
                            }
                            centerCoordinate={centerCoordinates}
                        />
                        <MapCluster
                            clubs={newClub.map((club: MapClub) => ({
                                ...club,
                                properties: {
                                    ...club.properties,
                                    isSelected: selectedClub?.clubs?.id === club.id,
                                },
                            }))}
                            onItemPress={(event: OnPressEvent) => {
                                onMarkerPressed(event as unknown as OnPressEvent);
                            }}
                        />
                    </MapboxGL.MapView>
                    <UserLocationButton setUserRegion={handleCurrentLocation} />
                    <ScreenResizeButton />
                </>
                {activeView === 'LIST' && (
                    <ListView
                        listItems={state?.boundariesChangeClub}
                        myClubs={newClub}
                        onPressClub={onPressClub}
                        setMapClubs={setMapClubs}
                    />
                )}
                <MapSearchResults
                    //@ts-ignore
                    searchedClubList={searchedClubList}
                    //@ts-ignore
                    locations={locations}
                    //@ts-ignore
                    searchInputState={searchInputState}
                    //@ts-ignore
                    searchingState={searchingState}
                    setSelectedClub={setSelectedClub}
                    setMapLocation={animateMapToBoundaries}
                    setActiveView={setActiveView}
                    setSearching={setSearching}
                    changeRegion={changeRegion}
                    setCenterCoordinates={setCenterCoordinates}
                    setSearchType={setSearchType}
                    activeView={activeView}
                />
                {state?.shouldDetailBottomSheetOpen && (
                    <ClubDetailsPopup
                        //@ts-ignore
                        club={selectedClub}
                        setSelectedClub={setSelectedClub}
                        setMoreDetailListView={setMoreDetailListView}
                        moreDetailListView={moreDetailListView}
                        setActiveView={setActiveView}
                        isComeFromMap={isComeFromMap}
                        recommendedClubs={recommendedClubs}
                    />
                )}
            </View>
        );
    }

    // @ts-ignore
    if (!locationPermissionGranted || locationPermissionGranted === 'never_ask_again') {
        return (
            <View style={styles.noLocPerms}>
                <View
                    style={{
                        flex: 1,
                        alignItems: 'center',
                        justifyContent: 'center',
                    }}>
                    <Text style={{ padding: 40, textAlign: 'center' }}>
                        You must enable location permissions in your phone's settings to use the Clubs map.
                    </Text>
                    <Button
                        title="Open Settings"
                        onPress={() => {
                            Linking.openSettings();
                        }}
                    />
                </View>
            </View>
        );
    }
}

export default memo(ClubMap);

const styles = StyleSheet.create({
    map: {
        flex: 1,
    },
    loader: {
        position: 'absolute',
        top: 0,
        bottom: 0,
        right: 0,
        left: 0,
        justifyContent: 'center',
        alignItems: 'center',
    },
    noLocPerms: {
        ...StyleSheet.absoluteFillObject,
        backgroundColor: 'white',
        borderRadius: 15,
        zIndex: 0,
    },
    marker: {
        shadowColor: 'black',
        shadowOffset: {
            width: 2,
            height: 0,
        },
        shadowOpacity: 0.5,
        shadowRadius: 2,
    },
    markerParent: {
        height: 40,
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        alignContent: 'flex-end',
    },
    markerText: {
        maxWidth: 120,
        fontWeight: 'bold',
    },
});
