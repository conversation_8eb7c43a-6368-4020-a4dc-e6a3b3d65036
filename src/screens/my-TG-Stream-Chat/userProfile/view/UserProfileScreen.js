import React, { useContext, useEffect, useMemo } from 'react';
import { ActivityIndicator, ScrollView, StatusBar, Text, TouchableOpacity, View } from 'react-native';

import { AuthContext } from '../../../../context/AuthContext';
import { GlobalContext } from '../../../../context/contextApi';
import { StreamChatContext } from '../../../../context/StreamChatContext';
import { getGolferId, getUserProfileData } from '../action/userProfileAction';
import StreamHeader from '../../../../components/layout/StreamHeader';
import styles from '../style/UserProfileStyle';
import UserProfileIcon from './UserProfileIcon';
import ProfileButtonComponent from './ProfileButtonComponent';
import UsersPersonalInformations from './UsersPersonalInformations';
import GolferClubsListingComponent from './GolferClubsListingComponent';
import GroupDetailBlockIcon from '../../../../assets/svg/GroupDetailBlockIcon.svg';
import { colors } from '../../../../theme/theme';
import { handleBlockUser, handleUnBlockUser } from '../../action';
import { handleOnPress } from '../../TGChatComponent/AlertComponent';
import BlockIconComponent from './BlockIconComponent';
import { BLOCK_USER_POPUP_TEXT, UNBLOCK_USER_POPUP_TEXT } from '../../client';
import { createOneToOneChatFrndScreen1 } from '../../../my-TG-friends/action/createOnetoOneChat';
import ProfileHeader from '../../../../components/layout/ProfileHeader';
import { Spacing } from '../../../../utils/responsiveUI';
import { ProfileInfoBGIcon } from '../../../../assets/svg';
import FastImage from 'react-native-fast-image';
import { localImages } from '../../../../assets/images';
import ProfileIcon from './ProfileIconComponent';

const UserProfileScreen = ({ route }) => {
    const { selectedUser, comeFromRequest = false } = route.params;
    const { user } = useContext(AuthContext);
    const { channel, updateBlockedChannel, setChannel } = useContext(StreamChatContext);
    const { state, actions } = useContext(GlobalContext);

    const {
        about_yourself,
        streamChannelId,
        youHaveBlocked,
        isBlocked,
        userClubs,
        userProfileObjectLength,
        blockText,
        id,
        isFriends,
        private_network_id,
        isPNMember,
        btnText,
        alertBoxText,
        tg_ambassador_visibility,
        profilePhoto,
        name,
    } = useMemo(() => {
        const { userProfile } = state;
        let userProfileObjectLength = Object.keys(userProfile)?.length;
        const {
            about_yourself,
            blockStatus: { streamChannelId, youHaveBlocked } = {},
            isBlocked,
            userClubs,
            id,
            isFriends,
            private_network_id,
            tg_ambassador_visibility,
            profilePhoto,
            name,
        } = ({} = userProfile);
        let blockText = isBlocked && youHaveBlocked ? 'Unblock User' : 'Block User';
        let btnText = isBlocked && youHaveBlocked ? 'Unblock' : 'Block';
        let alertBoxText = isBlocked && youHaveBlocked ? UNBLOCK_USER_POPUP_TEXT : BLOCK_USER_POPUP_TEXT;
        let isPNMember = userClubs?.length === 1 && private_network_id ? true : false;
        return {
            about_yourself,
            streamChannelId,
            youHaveBlocked,
            isBlocked,
            userClubs,
            userProfileObjectLength,
            blockText,
            id,
            isFriends,
            private_network_id,
            isPNMember,
            btnText,
            alertBoxText,
            tg_ambassador_visibility,
            profilePhoto,
            name,
        };
    }, [state?.userProfile]);

    useEffect(() => {
        actions?.setCurrentScreenName(null);
        handleGetProfileInfo();
    }, [state?.otherUser?.user?.name, state?.otherUser?.user?.image]);

    useEffect(() => {
        if (selectedUser?.id) {
            createOneToOneChatFrndScreen1(
                user?.id,
                selectedUser?.id,
                () => {},
                () => {},
                () => {},
                setChannel,
            );
        }
        return () => {
            if (comeFromRequest) {
                setChannel({});
            }
        };
    }, [selectedUser]);

    const handleGetProfileInfo = () => {
        let golfertId = getGolferId(user?.id, channel, selectedUser);
        getUserProfileData(user?.id, golfertId, actions);
    };

    const handleUpdateBlockedChannel = () => {
        updateBlockedChannel(channel);
    };

    const unBlockUser = () => {
        handleUnBlockUser(user?.id, channel?.data?.id, id, actions, handleGetProfileInfo);
    };
    const blockUser = () => {
        handleBlockUser(user?.id, channel?.data?.id, id, actions, handleGetProfileInfo);
    };

    const handlePress = () => {
        if (blockText === 'Unblock User') {
            handleOnPress(channel, unBlockUser, alertBoxText, btnText);
        } else if (blockText === 'Block User') {
            handleOnPress(channel, blockUser, alertBoxText, btnText);
        }
    };

    const HandleGolfClubRender = () => {
        if (userClubs?.length >= 1 || (userClubs?.length >= 1 && private_network_id === null))
            return <GolferClubsListingComponent />;
        return null;
    };

    // if (userProfileObjectLength && isBlocked && !youHaveBlocked) {
    //     return (
    //         <View style={styles.container}>
    //             <ProfileHeader
    //                 title={'Profile Info'}
    //                 headerTitleStyle={styles.headerTitleStyle}
    //                 backButtonFillColor={colors.lightBlack}
    //                 containerStyle={{
    //                     backgroundColor: colors.whiteRGB,
    //                     paddingBottom: Spacing.SCALE_10,
    //                 }}
    //             />
    //             <View style={styles.topSpace}></View>
    //             <ScrollView style={styles.box}>
    //                 <View style={{ backgroundColor: colors.white }}>
    //                     <UserProfileIcon />
    //                     <View style={{ marginBottom: 10 }}></View>
    //                 </View>
    //                 <View style={styles.aboutWrapper}>
    //                     <Text style={styles.aboutHeader}>About</Text>
    //                     <Text style={styles.aboutText}>{about_yourself}</Text>
    //                 </View>
    //                 <View style={styles.GroupDetailBlockIconStyle}>
    //                     <GroupDetailBlockIcon />
    //                     <Text style={styles.blockText}>This user has blocked you!</Text>
    //                 </View>
    //             </ScrollView>
    //         </View>
    //     );
    // } else if (userProfileObjectLength)
    //     return (
    //         <View style={styles.container}>
    //             <StreamHeader screenName={`Profile Info`} />
    //             <View style={styles.topSpace}></View>
    //             <ScrollView style={styles.box}>
    //                 <View style={{ backgroundColor: colors.white }}>
    //                     <UserProfileIcon />
    //                     <ProfileButtonComponent handleGetProfileInfo={handleGetProfileInfo} />
    //                     <View style={{ marginBottom: 5 }}></View>
    //                 </View>
    //                 <View style={styles.aboutWrapper}>
    //                     <Text style={styles.aboutHeader}>About</Text>
    //                     <Text style={styles.aboutText}>{about_yourself}</Text>
    //                 </View>

    //                 <View style={[styles.topSpace, { marginTop: 0 }]}></View>
    //                 <View style={{ backgroundColor: colors.white }}>
    //                     <UsersPersonalInformations />
    //                 </View>
    //                 <HandleGolfClubRender />
    //                 <View style={[styles.topSpace, { marginTop: 0 }]}></View>
    //                 {!isFriends && (
    //                     <BlockIconComponent
    //                         blockText={blockText}
    //                         handlePress={handlePress}
    //                         isBlocked={isBlocked}
    //                         youHaveBlocked={youHaveBlocked}
    //                     />
    //                 )}
    //             </ScrollView>
    //         </View>
    //     );
    // else {
    //     return (
    //         <View style={styles.loaderStyle}>
    //             <ActivityIndicator color={colors.darkteal} size="large" />
    //         </View>
    //     );
    // }
    return (
        <>
            <StatusBar barStyle="light-content" backgroundColor={colors.whiteRGB} />
            <View style={styles.container}>
                <ProfileHeader
                    title={'Profile Info'}
                    headerTitleStyle={styles.headerTitleStyle}
                    backButtonFillColor={colors.lightBlack}
                    containerStyle={{
                        backgroundColor: colors.whiteRGB,
                        paddingBottom: Spacing.SCALE_10,
                    }}
                />
                <ScrollView style={styles.bodyWrapper}>
                    <FastImage source={localImages.profileInfoBGIcon} style={styles.imageStyle} />
                    <View style={styles.body}>
                        <View style={{ backgroundColor: colors.white }}>
                            <ProfileIcon
                                profilePhoto={profilePhoto}
                                name={name}
                                tg_ambassador_visibility={tg_ambassador_visibility}
                            />
                            {/* <View style={{ marginBottom: 10 }}></View> */}
                        </View>
                    </View>
                </ScrollView>
            </View>
        </>
    );
};

export default UserProfileScreen;
